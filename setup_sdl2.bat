@echo off
echo Setting up SDL2 automatically...
echo.

REM Check if PowerShell is available
where powershell >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: PowerShell not found!
    echo Please install PowerShell or download SDL2 manually.
    pause
    exit /b 1
)

echo Running PowerShell script to download and setup SDL2...
echo This may take a few minutes depending on your internet connection.
echo.

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "setup_sdl2.ps1"

echo.
echo Setup complete! You can now run build.bat to compile the project.
pause
