#include "FBXLoader.h"
#include <iostream>
#include <fstream>
#include <sstream>

// FBXModel implementation
void FBXModel::initializeAnimationSystem() {
    animationSystem.setSceneRoot(rootNode);
    animationSystem.setBoneMapping(boneMapping);
    
    for (const auto& animation : animations) {
        animationSystem.addAnimationClip(animation);
    }
    
    // Auto-play first animation if available
    if (!animations.empty()) {
        animationSystem.playAnimation(animations[0].name, true);
        std::cout << "Auto-playing animation: " << animations[0].name << std::endl;
    }
}

void FBXModel::update(float deltaTime) {
    animationSystem.update(deltaTime);
}

void FBXModel::cleanup(VkDevice device) {
    for (auto& mesh : meshes) {
        mesh.cleanup(device);
    }
}

// FBXLoader implementation
FBXLoader::FBXLoader() {
#ifdef FBXSDK_SHARED
    initializeFBXSDK();
#endif
}

FBXLoader::~FBXLoader() {
#ifdef FBXSDK_SHARED
    cleanupFBXSDK();
#endif
}

bool FBXLoader::loadFBX(const std::string& filename, FBXModel& model) {
#ifdef FBXSDK_SHARED
    if (!fbxManager) {
        lastError = "FBX SDK not initialized";
        return false;
    }

    FbxImporter* importer = FbxImporter::Create(fbxManager, "");
    if (!importer->Initialize(filename.c_str(), -1, fbxManager->GetIOSettings())) {
        lastError = "Failed to initialize FBX importer: " + std::string(importer->GetStatus().GetErrorString());
        importer->Destroy();
        return false;
    }

    FbxScene* scene = FbxScene::Create(fbxManager, "myScene");
    if (!importer->Import(scene)) {
        lastError = "Failed to import FBX scene: " + std::string(importer->GetStatus().GetErrorString());
        importer->Destroy();
        scene->Destroy();
        return false;
    }

    importer->Destroy();

    // Process the scene
    processScene(scene, model);
    
    scene->Destroy();
    
    std::cout << "Successfully loaded FBX file: " << filename << std::endl;
    std::cout << "Meshes: " << model.meshes.size() << std::endl;
    std::cout << "Animations: " << model.animations.size() << std::endl;
    
    return true;
#else
    return loadFBXFallback(filename, model);
#endif
}

#ifdef FBXSDK_SHARED
bool FBXLoader::initializeFBXSDK() {
    fbxManager = FbxManager::Create();
    if (!fbxManager) {
        lastError = "Failed to create FBX manager";
        return false;
    }

    ioSettings = FbxIOSettings::Create(fbxManager, IOSROOT);
    fbxManager->SetIOSettings(ioSettings);

    return true;
}

void FBXLoader::cleanupFBXSDK() {
    if (fbxManager) {
        fbxManager->Destroy();
        fbxManager = nullptr;
        ioSettings = nullptr;
    }
}

void FBXLoader::processScene(FbxScene* scene, FBXModel& model) {
    FbxNode* rootNode = scene->GetRootNode();
    if (rootNode) {
        processNode(rootNode, model.rootNode, model);
        buildBoneMapping(scene, model);
        processAnimations(scene, model);
    }
}

void FBXLoader::processNode(FbxNode* node, SceneNode& sceneNode, FBXModel& model) {
    sceneNode.name = node->GetName();
    sceneNode.transformation = fbxMatrixToGlm(node->EvaluateGlobalTransform());

    // Process meshes
    for (int i = 0; i < node->GetNodeAttributeCount(); i++) {
        FbxNodeAttribute* attribute = node->GetNodeAttributeByIndex(i);
        if (attribute->GetAttributeType() == FbxNodeAttribute::eMesh) {
            FbxMesh* fbxMesh = static_cast<FbxMesh*>(attribute);
            Mesh mesh;
            processMesh(fbxMesh, mesh, model);
            sceneNode.meshIndices.push_back(model.meshes.size());
            model.meshes.push_back(std::move(mesh));
        }
    }

    // Process children
    for (int i = 0; i < node->GetChildCount(); i++) {
        SceneNode childNode;
        childNode.parent = &sceneNode;
        processNode(node->GetChild(i), childNode, model);
        sceneNode.children.push_back(std::move(childNode));
    }
}

void FBXLoader::processMesh(FbxMesh* fbxMesh, Mesh& mesh, FBXModel& model) {
    processVertices(fbxMesh, mesh);
    processNormals(fbxMesh, mesh);
    processUVs(fbxMesh, mesh);
    processBones(fbxMesh, mesh, model);

    // Process material
    FbxNode* node = fbxMesh->GetNode();
    if (node && node->GetMaterialCount() > 0) {
        FbxSurfaceMaterial* material = node->GetMaterial(0);
        processMaterial(material, mesh.material);
    }
}

void FBXLoader::processVertices(FbxMesh* fbxMesh, Mesh& mesh) {
    int vertexCount = fbxMesh->GetControlPointsCount();
    FbxVector4* controlPoints = fbxMesh->GetControlPoints();

    mesh.vertices.resize(vertexCount);
    for (int i = 0; i < vertexCount; i++) {
        mesh.vertices[i].position = fbxVectorToGlm(controlPoints[i]);
        mesh.vertices[i].boneIds = glm::ivec4(-1);
        mesh.vertices[i].boneWeights = glm::vec4(0.0f);
    }

    // Process indices
    int polygonCount = fbxMesh->GetPolygonCount();
    for (int i = 0; i < polygonCount; i++) {
        int polygonSize = fbxMesh->GetPolygonSize(i);
        if (polygonSize == 3) {
            for (int j = 0; j < 3; j++) {
                int vertexIndex = fbxMesh->GetPolygonVertex(i, j);
                mesh.indices.push_back(vertexIndex);
            }
        }
    }
}

// Additional FBX processing methods would go here...
// For brevity, I'm showing the structure but not implementing all methods

glm::mat4 FBXLoader::fbxMatrixToGlm(const FbxAMatrix& fbxMatrix) {
    glm::mat4 result;
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 4; j++) {
            result[i][j] = static_cast<float>(fbxMatrix.Get(i, j));
        }
    }
    return result;
}

glm::vec3 FBXLoader::fbxVectorToGlm(const FbxVector4& fbxVector) {
    return glm::vec3(
        static_cast<float>(fbxVector[0]),
        static_cast<float>(fbxVector[1]),
        static_cast<float>(fbxVector[2])
    );
}

#endif

bool FBXLoader::loadFBXFallback(const std::string& filename, FBXModel& model) {
    lastError = "FBX SDK not available. Creating a simple test cube instead.";
    
    // Create a simple test cube
    Mesh cubeMesh;
    
    // Cube vertices
    cubeMesh.vertices = {
        // Front face
        {{-1.0f, -1.0f,  1.0f}, {0.0f, 0.0f, 1.0f}, {0.0f, 0.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}},
        {{ 1.0f, -1.0f,  1.0f}, {0.0f, 0.0f, 1.0f}, {1.0f, 0.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}},
        {{ 1.0f,  1.0f,  1.0f}, {0.0f, 0.0f, 1.0f}, {1.0f, 1.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}},
        {{-1.0f,  1.0f,  1.0f}, {0.0f, 0.0f, 1.0f}, {0.0f, 1.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}},
        
        // Back face
        {{-1.0f, -1.0f, -1.0f}, {0.0f, 0.0f, -1.0f}, {1.0f, 0.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}},
        {{-1.0f,  1.0f, -1.0f}, {0.0f, 0.0f, -1.0f}, {1.0f, 1.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}},
        {{ 1.0f,  1.0f, -1.0f}, {0.0f, 0.0f, -1.0f}, {0.0f, 1.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}},
        {{ 1.0f, -1.0f, -1.0f}, {0.0f, 0.0f, -1.0f}, {0.0f, 0.0f}, {-1, -1, -1, -1}, {0.0f, 0.0f, 0.0f, 0.0f}}
    };
    
    // Cube indices
    cubeMesh.indices = {
        0, 1, 2, 2, 3, 0,   // Front face
        4, 5, 6, 6, 7, 4,   // Back face
        3, 2, 6, 6, 5, 3,   // Top face
        0, 4, 7, 7, 1, 0,   // Bottom face
        0, 3, 5, 5, 4, 0,   // Left face
        1, 7, 6, 6, 2, 1    // Right face
    };
    
    model.meshes.push_back(std::move(cubeMesh));
    
    // Create simple root node
    model.rootNode.name = "Root";
    model.rootNode.transformation = glm::mat4(1.0f);
    model.rootNode.meshIndices.push_back(0);
    
    std::cout << "Created fallback test cube (FBX SDK not available)" << std::endl;
    return true;
}
