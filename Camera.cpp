#include "Camera.h"
#include <iostream>
#include <algorithm>

Camera::Camera(glm::vec3 pos, glm::vec3 worldUpVec, float yawAngle, float pitchAngle)
    : position(pos)
    , worldUp(worldUpVec)
    , yaw(yawAngle)
    , pitch(pitchAngle)
    , movementSpeed(DEFAULT_SPEED)
    , mouseSensitivity(DEFAULT_SENSITIVITY)
    , zoom(DEFAULT_ZOOM)
{
    updateCameraVectors();
}

glm::mat4 Camera::getViewMatrix() const {
    return glm::lookAt(position, position + front, up);
}

void Camera::processKeyboard(CameraMovement direction, float deltaTime) {
    float velocity = movementSpeed * deltaTime;
    
    switch (direction) {
        case CameraMovement::FORWARD:
            position += front * velocity;
            break;
        case CameraMovement::BACKWARD:
            position -= front * velocity;
            break;
        case CameraMovement::LEFT:
            position -= right * velocity;
            break;
        case CameraMovement::RIGHT:
            position += right * velocity;
            break;
        case CameraMovement::UP:
            position += worldUp * velocity;
            break;
        case CameraMovement::DOWN:
            position -= worldUp * velocity;
            break;
    }
}

void Camera::processMouseMovement(float xOffset, float yOffset, bool constrainPitch) {
    xOffset *= mouseSensitivity;
    yOffset *= mouseSensitivity;

    yaw += xOffset;
    pitch += yOffset;

    // Constrain pitch to prevent screen flipping
    if (constrainPitch) {
        pitch = std::clamp(pitch, MIN_PITCH, MAX_PITCH);
    }

    // Update front, right and up vectors using the updated Euler angles
    updateCameraVectors();
}

void Camera::processMouseScroll(float yOffset) {
    zoom -= yOffset;
    zoom = std::clamp(zoom, MIN_ZOOM, MAX_ZOOM);
}

glm::vec3 Camera::getTarget() const {
    return position + front;
}

void Camera::setPosition(const glm::vec3& pos) {
    position = pos;
}

void Camera::lookAt(const glm::vec3& target) {
    glm::vec3 direction = glm::normalize(target - position);
    
    // Calculate yaw and pitch from direction vector
    yaw = glm::degrees(atan2(direction.z, direction.x));
    pitch = glm::degrees(asin(-direction.y));
    
    // Constrain pitch
    pitch = std::clamp(pitch, MIN_PITCH, MAX_PITCH);
    
    updateCameraVectors();
}

void Camera::reset() {
    position = glm::vec3(5.0f, 3.0f, 5.0f);
    yaw = DEFAULT_YAW;
    pitch = DEFAULT_PITCH;
    zoom = DEFAULT_ZOOM;
    updateCameraVectors();
}

void Camera::printInfo() const {
    std::cout << "Camera Info:" << std::endl;
    std::cout << "  Position: (" << position.x << ", " << position.y << ", " << position.z << ")" << std::endl;
    std::cout << "  Front: (" << front.x << ", " << front.y << ", " << front.z << ")" << std::endl;
    std::cout << "  Yaw: " << yaw << ", Pitch: " << pitch << std::endl;
    std::cout << "  Zoom: " << zoom << std::endl;
}

void Camera::updateCameraVectors() {
    // Calculate the new front vector
    glm::vec3 newFront;
    newFront.x = cos(glm::radians(yaw)) * cos(glm::radians(pitch));
    newFront.y = sin(glm::radians(pitch));
    newFront.z = sin(glm::radians(yaw)) * cos(glm::radians(pitch));
    front = glm::normalize(newFront);
    
    // Re-calculate the right and up vector
    right = glm::normalize(glm::cross(front, worldUp));
    up = glm::normalize(glm::cross(right, front));
}
