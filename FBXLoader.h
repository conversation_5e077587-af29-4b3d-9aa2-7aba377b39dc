#pragma once

#include "Mesh.h"
#include "Animation.h"
#include <string>
#include <vector>
#include <map>

#ifdef FBXSDK_SHARED
#include <fbxsdk.h>
#endif

// FBX model container
struct FBXModel {
    std::vector<Mesh> meshes;
    std::vector<AnimationClip> animations;
    SceneNode rootNode;
    std::map<std::string, int> boneMapping;
    
    // Model transformation
    glm::mat4 modelMatrix = glm::mat4(1.0f);
    
    // Animation system
    AnimationSystem animationSystem;
    
    // Initialize animation system
    void initializeAnimationSystem();
    
    // Update animations
    void update(float deltaTime);
    
    // Cleanup resources
    void cleanup(VkDevice device);
};

// FBX Loader class
class FBXLoader {
public:
    FBXLoader();
    ~FBXLoader();

    // Load FBX file
    bool loadFBX(const std::string& filename, FBXModel& model);

    // Get last error message
    const std::string& getLastError() const { return lastError; }

private:
#ifdef FBXSDK_SHARED
    FbxManager* fbxManager = nullptr;
    FbxIOSettings* ioSettings = nullptr;
    
    // Processing functions
    bool initializeFBXSDK();
    void cleanupFBXSDK();
    
    // Scene processing
    void processScene(FbxScene* scene, FBXModel& model);
    void processNode(FbxNode* node, SceneNode& sceneNode, FBXModel& model);
    
    // Mesh processing
    void processMesh(FbxMesh* fbxMesh, Mesh& mesh, FBXModel& model);
    void processVertices(FbxMesh* fbxMesh, Mesh& mesh);
    void processNormals(FbxMesh* fbxMesh, Mesh& mesh);
    void processUVs(FbxMesh* fbxMesh, Mesh& mesh);
    void processBones(FbxMesh* fbxMesh, Mesh& mesh, FBXModel& model);
    void processMaterial(FbxSurfaceMaterial* fbxMaterial, Material& material);
    
    // Animation processing
    void processAnimations(FbxScene* scene, FBXModel& model);
    void processAnimationStack(FbxAnimStack* animStack, FbxScene* scene, FBXModel& model);
    void processAnimationLayer(FbxAnimLayer* animLayer, FbxScene* scene, AnimationClip& clip);
    void processAnimationCurve(FbxAnimCurve* curve, std::vector<PositionKey>& keys);
    void processAnimationCurve(FbxAnimCurve* curve, std::vector<RotationKey>& keys);
    void processAnimationCurve(FbxAnimCurve* curve, std::vector<ScalingKey>& keys);
    
    // Utility functions
    glm::mat4 fbxMatrixToGlm(const FbxAMatrix& fbxMatrix);
    glm::vec3 fbxVectorToGlm(const FbxVector4& fbxVector);
    glm::quat fbxQuatToGlm(const FbxQuaternion& fbxQuat);
    
    // Bone mapping
    void buildBoneMapping(FbxScene* scene, FBXModel& model);
    int getBoneIndex(const std::string& boneName, FBXModel& model);
#endif

    std::string lastError;
    
    // Fallback functions when FBX SDK is not available
    bool loadFBXFallback(const std::string& filename, FBXModel& model);
};
