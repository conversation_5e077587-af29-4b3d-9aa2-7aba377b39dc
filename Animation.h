#pragma once

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/quaternion.hpp>
#include <vector>
#include <string>
#include <map>

// Key frame for position animation
struct PositionKey {
    double time;
    glm::vec3 position;
};

// Key frame for rotation animation
struct RotationKey {
    double time;
    glm::quat rotation;
};

// Key frame for scaling animation
struct ScalingKey {
    double time;
    glm::vec3 scaling;
};

// Animation channel for a single bone/node
struct AnimationChannel {
    std::string nodeName;
    std::vector<PositionKey> positionKeys;
    std::vector<RotationKey> rotationKeys;
    std::vector<ScalingKey> scalingKeys;

    // Get interpolated transformation matrix at given time
    glm::mat4 getTransformationMatrix(double animationTime) const;

private:
    // Interpolation functions
    glm::vec3 interpolatePosition(double animationTime) const;
    glm::quat interpolateRotation(double animationTime) const;
    glm::vec3 interpolateScaling(double animationTime) const;

    // Find key frame indices for interpolation
    int findPositionIndex(double animationTime) const;
    int findRotationIndex(double animationTime) const;
    int findScalingIndex(double animationTime) const;
};

// Animation clip containing all channels
struct AnimationClip {
    std::string name;
    double duration;        // Duration in seconds
    double ticksPerSecond;  // Animation speed
    std::vector<AnimationChannel> channels;
    
    // Get channel by node name
    const AnimationChannel* getChannel(const std::string& nodeName) const;
};

// Node in the scene hierarchy
struct SceneNode {
    std::string name;
    glm::mat4 transformation;
    std::vector<int> meshIndices;
    std::vector<SceneNode> children;
    SceneNode* parent = nullptr;

    // Calculate global transformation
    glm::mat4 getGlobalTransformation() const;
};

// Animation system class
class AnimationSystem {
public:
    AnimationSystem();
    ~AnimationSystem();

    // Load animation data
    void addAnimationClip(const AnimationClip& clip);
    
    // Play animation
    void playAnimation(const std::string& animationName, bool loop = true);
    void stopAnimation();
    void pauseAnimation();
    void resumeAnimation();

    // Update animation state
    void update(float deltaTime);

    // Get current bone transformations
    const std::vector<glm::mat4>& getBoneTransformations() const;

    // Set scene root node
    void setSceneRoot(const SceneNode& root) { sceneRoot = root; }

    // Set bone mapping
    void setBoneMapping(const std::map<std::string, int>& mapping) { boneMapping = mapping; }

    // Animation state
    bool isPlaying() const { return playing; }
    bool isPaused() const { return paused; }
    float getCurrentTime() const { return currentTime; }
    float getDuration() const;

private:
    std::vector<AnimationClip> animationClips;
    std::map<std::string, int> boneMapping;  // Bone name to index mapping
    std::vector<glm::mat4> boneTransformations;
    SceneNode sceneRoot;

    // Animation state
    int currentAnimationIndex = -1;
    float currentTime = 0.0f;
    bool playing = false;
    bool paused = false;
    bool looping = true;

    // Update bone transformations recursively
    void updateBoneTransformations(const SceneNode& node, const glm::mat4& parentTransform, 
                                  const AnimationClip* currentClip);

    // Find animation clip by name
    int findAnimationClip(const std::string& name) const;
};
