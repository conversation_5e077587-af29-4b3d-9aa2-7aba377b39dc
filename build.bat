@echo off
echo Building Vulkan SDL Application...
echo.

REM Check if dependencies are installed
echo Checking dependencies...
where cmake >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: CMake not found! Please install CMake first.
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)

where vulkaninfo >nul 2>&1
if %errorLevel% neq 0 (
    echo Warning: Vulkan SDK not found in PATH
    echo Please make sure Vulkan SDK is installed
    echo Download from: https://vulkan.lunarg.com/
)

if not exist "C:\SDL2\include\SDL2\SDL.h" (
    echo Error: SDL2 not found at C:\SDL2\
    echo Please run install_dependencies.bat first
    echo Or manually install SDL2 to C:\SDL2\
    pause
    exit /b 1
)

echo ✓ All dependencies found
echo.

REM Create build directory
echo Creating build directory...
if not exist build mkdir build
cd build

REM Configure with CMake
echo Configuring with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if %errorLevel% neq 0 (
    echo Error: CMake configuration failed!
    echo Check the error messages above.
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release
if %errorLevel% neq 0 (
    echo Error: Build failed!
    echo Check the error messages above.
    pause
    exit /b 1
)

echo.
echo ✓ Build complete!
echo Executable location: build\bin\Release\VulkanSDLWindow.exe
echo.

REM Check if executable exists
if exist "bin\Release\VulkanSDLWindow.exe" (
    echo Would you like to run the application now? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo Starting application...
        bin\Release\VulkanSDLWindow.exe
    )
) else (
    echo Warning: Executable not found at expected location
    echo Please check the build output above for errors
)

pause
