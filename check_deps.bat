@echo off
echo Checking dependencies for Vulkan SDL Application...
echo.

echo Checking for Vulkan SDK...
where vulkaninfo >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Vulkan SDK found
) else (
    echo [MISSING] Vulkan SDK not found
    echo Download from: https://vulkan.lunarg.com/
)

echo.
echo Checking for SDL2...
if exist "C:\SDL2\include\SDL2\SDL.h" (
    echo [OK] SDL2 found at C:\SDL2\
) else (
    echo [MISSING] SDL2 not found at C:\SDL2\
    echo Download from: https://www.libsdl.org/download-2.0.php
    echo Extract to C:\SDL2\ directory
)

echo.
echo Checking for CMake...
where cmake >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] CMake found
) else (
    echo [MISSING] CMake not found
    echo Download from: https://cmake.org/download/
)

echo.
echo Checking for Visual Studio...
where cl >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Visual Studio compiler found
) else (
    echo [MISSING] Visual Studio compiler not found
    echo Install Visual Studio 2019/2022 with C++ tools
)

echo.
echo Dependency check complete!
echo If any dependencies are missing, please install them before building.
