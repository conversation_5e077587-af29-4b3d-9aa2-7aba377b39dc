# PowerShell script to automatically download and setup GLM
param(
    [string]$InstallPath = "C:\glm"
)

Write-Host "GLM Auto-Setup Script" -ForegroundColor Green
Write-Host "====================" -ForegroundColor Green
Write-Host ""

# GLM download information
$glmVersion = "*******"
$glmUrl = "https://github.com/g-truc/glm/releases/download/$glmVersion/glm-$glmVersion.zip"
$tempDir = "$env:TEMP\GLMSetup"
$zipFile = "$tempDir\glm.zip"

Write-Host "Setting up GLM version $glmVersion" -ForegroundColor Cyan
Write-Host "Install path: $InstallPath" -ForegroundColor Cyan
Write-Host ""

# Create temporary directory
Write-Host "Creating temporary directory..." -ForegroundColor Yellow
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Download GLM
Write-Host "Downloading GLM from GitHub..." -ForegroundColor Yellow
Write-Host "URL: $glmUrl" -ForegroundColor Gray

try {
    $ProgressPreference = 'SilentlyContinue'
    Invoke-WebRequest -Uri $glmUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "✓ Download completed successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual download instructions:" -ForegroundColor Yellow
    Write-Host "1. Visit: https://github.com/g-truc/glm/releases" -ForegroundColor White
    Write-Host "2. Download: glm-$glmVersion.zip" -ForegroundColor White
    Write-Host "3. Extract to: $InstallPath" -ForegroundColor White
    exit 1
}

# Extract GLM
Write-Host "Extracting GLM..." -ForegroundColor Yellow

try {
    # Create install directory
    if (Test-Path $InstallPath) {
        Write-Host "Removing existing GLM installation..." -ForegroundColor Yellow
        Remove-Item $InstallPath -Recurse -Force
    }
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null

    # Extract zip file
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($zipFile, $tempDir)
    
    # Find the extracted GLM folder
    $extractedFolder = Get-ChildItem $tempDir -Directory | Where-Object { $_.Name -like "glm*" } | Select-Object -First 1
    
    if ($extractedFolder) {
        # Copy contents to install path
        Copy-Item "$($extractedFolder.FullName)\*" $InstallPath -Recurse -Force
        Write-Host "✓ Extraction completed successfully" -ForegroundColor Green
    } else {
        throw "Could not find extracted GLM folder"
    }
} catch {
    Write-Host "✗ Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify installation
Write-Host "Verifying installation..." -ForegroundColor Yellow

$requiredFiles = @(
    "$InstallPath\glm\glm.hpp",
    "$InstallPath\glm\gtc\matrix_transform.hpp",
    "$InstallPath\glm\gtc\quaternion.hpp"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing: $file" -ForegroundColor Red
        $allFilesExist = $false
    }
}

# Cleanup
Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
Remove-Item $tempDir -Recurse -Force

# Final status
Write-Host ""
if ($allFilesExist) {
    Write-Host "🎉 GLM setup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "GLM is now available at: $InstallPath" -ForegroundColor Cyan
} else {
    Write-Host "❌ GLM setup failed!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check the error messages above and try again." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
