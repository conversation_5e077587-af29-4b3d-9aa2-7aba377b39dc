@echo off
echo Installing dependencies for Vulkan SDL Application...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as administrator - Good!
) else (
    echo This script should be run as administrator for best results.
    echo You can continue, but some operations might fail.
    pause
)

echo.
echo Checking for Vulkan SDK...
where vulkaninfo >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Vulkan SDK found
    vulkaninfo --summary
) else (
    echo ✗ Vulkan SDK not found
    echo Please download and install from: https://vulkan.lunarg.com/
    echo.
)

echo.
echo Checking for SDL2...
if exist "C:\SDL2\include\SDL2\SDL.h" (
    echo ✓ SDL2 found at C:\SDL2\
) else (
    echo ✗ SDL2 not found at C:\SDL2\
    echo.
    echo Would you like to download SDL2 automatically? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo Downloading SDL2...
        
        REM Create SDL2 directory
        mkdir C:\SDL2 2>nul
        
        REM Download SDL2 (you might need to update this URL for the latest version)
        echo Please manually download SDL2 from: https://www.libsdl.org/download-2.0.php
        echo Download "Development Libraries" for Windows (SDL2-devel-2.x.x-VC.zip)
        echo Extract to C:\SDL2\ directory
        echo.
        echo Press any key when done...
        pause
    )
)

echo.
echo Checking for CMake...
where cmake >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ CMake found
    cmake --version
) else (
    echo ✗ CMake not found
    echo Please download and install from: https://cmake.org/download/
    echo.
)

echo.
echo Checking for Visual Studio...
where cl >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Visual Studio compiler found
) else (
    echo ✗ Visual Studio compiler not found
    echo Please install Visual Studio 2019 or 2022 with C++ development tools
    echo Or install Visual Studio Build Tools
    echo.
)

echo.
echo Dependency check complete!
echo.
echo If all dependencies are installed, you can now run:
echo   build.bat
echo.
pause
