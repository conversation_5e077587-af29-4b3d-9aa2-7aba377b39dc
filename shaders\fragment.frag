#version 450

layout(binding = 0) uniform UniformBufferObject {
    mat4 model;
    mat4 view;
    mat4 proj;
    mat4 bones[100];

    // Lighting data
    vec3 lightDirection;
    float lightIntensity;
    vec3 lightColor;
    float padding1;
    vec3 ambientLight;
    float padding2;
    vec3 cameraPosition;
    float padding3;
} ubo;

layout(location = 0) in vec3 fragColor;
layout(location = 1) in vec2 fragTexCoord;
layout(location = 2) in vec3 fragNormal;
layout(location = 3) in vec3 fragWorldPos;
layout(location = 4) in vec3 fragViewPos;

layout(location = 0) out vec4 outColor;

void main() {
    // Normalize inputs
    vec3 normal = normalize(fragNormal);
    vec3 lightDir = normalize(-ubo.lightDirection); // Light direction points towards light
    vec3 viewDir = normalize(ubo.cameraPosition - fragWorldPos);

    // Material properties
    vec3 materialDiffuse = fragColor;
    vec3 materialSpecular = vec3(0.3, 0.3, 0.3);
    float shininess = 32.0;

    // Ambient lighting
    vec3 ambient = ubo.ambientLight * materialDiffuse;

    // Diffuse lighting
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * ubo.lightColor * ubo.lightIntensity * materialDiffuse;

    // Specular lighting (Blinn-Phong)
    vec3 halfwayDir = normalize(lightDir + viewDir);
    float spec = pow(max(dot(normal, halfwayDir), 0.0), shininess);
    vec3 specular = spec * ubo.lightColor * ubo.lightIntensity * materialSpecular;

    // Combine all lighting components
    vec3 result = ambient + diffuse + specular;

    // Add some fog effect based on distance
    float distance = length(fragViewPos);
    float fogFactor = exp(-distance * 0.02);
    fogFactor = clamp(fogFactor, 0.0, 1.0);

    vec3 fogColor = vec3(0.7, 0.8, 0.9);
    result = mix(fogColor, result, fogFactor);

    outColor = vec4(result, 1.0);
}
