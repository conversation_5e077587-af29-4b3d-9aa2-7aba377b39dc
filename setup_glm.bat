@echo off
echo Setting up GLM automatically...
echo.

REM Check if PowerShell is available
where powershell >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: PowerShell not found!
    echo Please install PowerShell or download GLM manually.
    pause
    exit /b 1
)

echo Running PowerShell script to download and setup GLM...
echo This may take a few minutes depending on your internet connection.
echo.

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "setup_glm.ps1"

echo.
echo GLM setup complete!
pause
