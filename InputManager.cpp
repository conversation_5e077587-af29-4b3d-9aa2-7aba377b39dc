#include "InputManager.h"
#include <iostream>

InputManager::InputManager()
    : window(nullptr)
    , mouseCaptured(false)
    , firstMouse(true)
    , lastMouseX(0.0f)
    , lastMouseY(0.0f)
    , mouseSensitivity(0.1f)
    , cameraSpeed(5.0f)
{
}

InputManager::~InputManager() {
    if (mouseCaptured) {
        SDL_SetRelativeMouseMode(SDL_FALSE);
        SDL_ShowCursor(SDL_ENABLE);
    }
}

void InputManager::initialize(SDL_Window* sdlWindow) {
    window = sdlWindow;
    
    // Get initial window size for mouse positioning
    int windowWidth, windowHeight;
    SDL_GetWindowSize(window, &windowWidth, &windowHeight);
    lastMouseX = windowWidth / 2.0f;
    lastMouseY = windowHeight / 2.0f;
    
    // Enable mouse capture by default
    toggleMouseCapture();
    
    std::cout << "Input Manager initialized" << std::endl;
    std::cout << "Controls:" << std::endl;
    std::cout << "  WASD - Move camera" << std::endl;
    std::cout << "  QE - Move up/down" << std::endl;
    std::cout << "  Mouse - Look around" << std::endl;
    std::cout << "  Tab - Toggle mouse capture" << std::endl;
    std::cout << "  R - Reset camera" << std::endl;
    std::cout << "  Shift - Move faster" << std::endl;
    std::cout << "  Ctrl - Move slower" << std::endl;
}

bool InputManager::processEvents(Camera& camera, float deltaTime) {
    SDL_Event event;
    
    while (SDL_PollEvent(&event)) {
        switch (event.type) {
            case SDL_QUIT:
                return false;
                
            case SDL_KEYDOWN:
                updateKeyState(event.key.keysym.scancode, true);
                
                // Handle special key actions
                if (event.key.keysym.sym == SDLK_ESCAPE) {
                    return false;
                } else if (event.key.keysym.sym == SDLK_TAB) {
                    toggleMouseCapture();
                } else if (event.key.keysym.sym == SDLK_r) {
                    camera.reset();
                    std::cout << "Camera reset" << std::endl;
                } else if (event.key.keysym.sym == SDLK_f) {
                    // F key for loading FBX (handled in main application)
                    // We'll pass this through a callback system
                } else if (event.key.keysym.sym == SDLK_SPACE) {
                    // Space key for loading test model (handled in main application)
                }
                break;
                
            case SDL_KEYUP:
                updateKeyState(event.key.keysym.scancode, false);
                break;
                
            case SDL_MOUSEMOTION:
                if (mouseCaptured) {
                    handleMouseMotion(camera, event.motion.xrel, event.motion.yrel);
                }
                break;
                
            case SDL_MOUSEWHEEL:
                camera.processMouseScroll(event.wheel.y);
                break;
                
            case SDL_WINDOWEVENT:
                if (event.window.event == SDL_WINDOWEVENT_RESIZED) {
                    std::cout << "Window resized to: " 
                              << event.window.data1 << "x" << event.window.data2 << std::endl;
                }
                break;
        }
    }
    
    // Process continuous input (movement)
    processContinuousInput(camera, deltaTime);
    
    return true;
}

void InputManager::setMouseSensitivity(float sensitivity) {
    mouseSensitivity = sensitivity;
}

void InputManager::toggleMouseCapture() {
    mouseCaptured = !mouseCaptured;
    
    if (mouseCaptured) {
        SDL_SetRelativeMouseMode(SDL_TRUE);
        SDL_ShowCursor(SDL_DISABLE);
        firstMouse = true;
        std::cout << "Mouse captured - use Tab to release" << std::endl;
    } else {
        SDL_SetRelativeMouseMode(SDL_FALSE);
        SDL_ShowCursor(SDL_ENABLE);
        std::cout << "Mouse released - use Tab to capture" << std::endl;
    }
}

void InputManager::setCameraSpeed(float speed) {
    cameraSpeed = speed;
}

void InputManager::setKeyBinding(SDL_Scancode key, std::function<void()> action) {
    keyBindings[key] = action;
}

void InputManager::processContinuousInput(Camera& camera, float deltaTime) {
    float currentSpeed = cameraSpeed;
    
    // Speed modifiers
    if (isKeyPressed(SDL_SCANCODE_LSHIFT) || isKeyPressed(SDL_SCANCODE_RSHIFT)) {
        currentSpeed *= 3.0f; // Faster movement
    }
    if (isKeyPressed(SDL_SCANCODE_LCTRL) || isKeyPressed(SDL_SCANCODE_RCTRL)) {
        currentSpeed *= 0.3f; // Slower movement
    }
    
    // Temporarily set camera speed
    float originalSpeed = camera.movementSpeed;
    camera.movementSpeed = currentSpeed;
    
    // Movement keys
    if (isKeyPressed(SDL_SCANCODE_W)) {
        camera.processKeyboard(CameraMovement::FORWARD, deltaTime);
    }
    if (isKeyPressed(SDL_SCANCODE_S)) {
        camera.processKeyboard(CameraMovement::BACKWARD, deltaTime);
    }
    if (isKeyPressed(SDL_SCANCODE_A)) {
        camera.processKeyboard(CameraMovement::LEFT, deltaTime);
    }
    if (isKeyPressed(SDL_SCANCODE_D)) {
        camera.processKeyboard(CameraMovement::RIGHT, deltaTime);
    }
    if (isKeyPressed(SDL_SCANCODE_Q)) {
        camera.processKeyboard(CameraMovement::DOWN, deltaTime);
    }
    if (isKeyPressed(SDL_SCANCODE_E)) {
        camera.processKeyboard(CameraMovement::UP, deltaTime);
    }
    
    // Restore original speed
    camera.movementSpeed = originalSpeed;
}

void InputManager::handleMouseMotion(Camera& camera, float xrel, float yrel) {
    if (firstMouse) {
        firstMouse = false;
        return; // Ignore first mouse movement to avoid jump
    }
    
    // Process mouse movement
    camera.processMouseMovement(xrel, -yrel); // Invert Y axis
}

void InputManager::updateKeyState(SDL_Scancode scancode, bool pressed) {
    keyStates[scancode] = pressed;
}

bool InputManager::isKeyPressed(SDL_Scancode scancode) const {
    auto it = keyStates.find(scancode);
    return it != keyStates.end() && it->second;
}
