#pragma once

#include <string>
#include <vector>

// File filter structure
struct FileFilter {
    std::string description;
    std::string extension;
};

// File dialog class for opening files
class FileDialog {
public:
    FileDialog();
    ~FileDialog();

    // Show open file dialog
    std::string openFile(const std::string& title = "Open File", 
                        const std::vector<FileFilter>& filters = {});

    // Show save file dialog
    std::string saveFile(const std::string& title = "Save File",
                        const std::vector<FileFilter>& filters = {});

    // Get last error message
    const std::string& getLastError() const { return lastError; }

    // Static convenience functions
    static std::string openFBXFile();
    static std::string openModelFile();

private:
    std::string lastError;

#ifdef _WIN32
    // Windows-specific implementation
    std::string openFileWindows(const std::string& title, const std::vector<FileFilter>& filters);
    std::string saveFileWindows(const std::string& title, const std::vector<FileFilter>& filters);
    std::string buildFilterString(const std::vector<FileFilter>& filters);
#else
    // Cross-platform fallback (console-based)
    std::string openFileConsole(const std::string& title, const std::vector<FileFilter>& filters);
    std::string saveFileConsole(const std::string& title, const std::vector<FileFilter>& filters);
#endif
};
