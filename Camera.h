#pragma once

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <SDL.h>

enum class CameraMovement {
    FORWARD,
    BACKWARD,
    LEFT,
    RIGHT,
    UP,
    DOWN
};

class Camera {
public:
    // Camera attributes
    glm::vec3 position;
    glm::vec3 front;
    glm::vec3 up;
    glm::vec3 right;
    glm::vec3 worldUp;

    // Euler angles
    float yaw;
    float pitch;

    // Camera options
    float movementSpeed;
    float mouseSensitivity;
    float zoom;

    // Constructor
    Camera(glm::vec3 pos = glm::vec3(0.0f, 0.0f, 0.0f), 
           glm::vec3 worldUpVec = glm::vec3(0.0f, 1.0f, 0.0f), 
           float yawAngle = -90.0f, 
           float pitchAngle = 0.0f);

    // Returns the view matrix calculated using Euler angles and the LookAt matrix
    glm::mat4 getViewMatrix() const;

    // Processes input received from any keyboard-like input system
    void processKeyboard(CameraMovement direction, float deltaTime);

    // Processes input received from a mouse input system
    void processMouseMovement(float xOffset, float yOffset, bool constrainPitch = true);

    // Processes input received from a mouse scroll-wheel event
    void processMouseScroll(float yOffset);

    // Get camera target position (position + front)
    glm::vec3 getTarget() const;

    // Set camera position
    void setPosition(const glm::vec3& pos);

    // Look at a specific target
    void lookAt(const glm::vec3& target);

    // Reset camera to default position and orientation
    void reset();

    // Get camera information for debugging
    void printInfo() const;

private:
    // Calculates the front vector from the camera's (updated) Euler angles
    void updateCameraVectors();

    // Default camera values
    static constexpr float DEFAULT_YAW = -90.0f;
    static constexpr float DEFAULT_PITCH = 0.0f;
    static constexpr float DEFAULT_SPEED = 5.0f;
    static constexpr float DEFAULT_SENSITIVITY = 0.1f;
    static constexpr float DEFAULT_ZOOM = 45.0f;
    static constexpr float MIN_ZOOM = 1.0f;
    static constexpr float MAX_ZOOM = 90.0f;
    static constexpr float MIN_PITCH = -89.0f;
    static constexpr float MAX_PITCH = 89.0f;
};
