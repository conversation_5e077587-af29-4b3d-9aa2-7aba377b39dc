#pragma once

#include <SDL.h>
#include <unordered_map>
#include <functional>
#include "Camera.h"

class InputManager {
public:
    InputManager();
    ~InputManager();

    // Initialize input manager
    void initialize(SDL_Window* window);

    // Process SDL events
    bool processEvents(Camera& camera, float deltaTime);

    // Mouse control
    void setMouseSensitivity(float sensitivity);
    void toggleMouseCapture();
    bool isMouseCaptured() const { return mouseCaptured; }

    // Camera control settings
    void setCameraSpeed(float speed);
    
    // Key bindings
    void setKeyBinding(SDL_Scancode key, std::function<void()> action);

private:
    SDL_Window* window;
    bool mouseCaptured;
    bool firstMouse;
    float lastMouseX;
    float lastMouseY;
    float mouseSensitivity;
    float cameraSpeed;

    // Key states
    std::unordered_map<SDL_Scancode, bool> keyStates;
    std::unordered_map<SDL_Scancode, std::function<void()>> keyBindings;

    // Process continuous key input (for movement)
    void processContinuousInput(Camera& camera, float deltaTime);

    // Handle mouse motion
    void handleMouseMotion(Camera& camera, float xrel, float yrel);

    // Update key state
    void updateKeyState(SDL_Scancode scancode, bool pressed);

    // Check if key is currently pressed
    bool isKeyPressed(SDL_Scancode scancode) const;
};
