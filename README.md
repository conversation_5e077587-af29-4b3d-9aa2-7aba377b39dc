# Vulkan SDL Window Application

这是一个使用Vulkan和SDL2创建窗口的C++应用程序。程序会创建一个黑色的窗口，展示Vulkan和SDL2的基本集成。

## 功能特性

- 使用SDL2创建窗口
- Vulkan实例和设备初始化
- 交换链创建和管理
- 基本的渲染循环（清屏为黑色）
- 正确的资源清理
- 窗口事件处理（关闭、ESC键退出）

## 系统要求

### Windows
- Windows 10/11
- Visual Studio 2019/2022 或 MinGW
- CMake 3.16+
- Vulkan SDK
- SDL2 开发库

### 安装依赖

#### 1. Vulkan SDK
从 [LunarG](https://vulkan.lunarg.com/) 下载并安装Vulkan SDK。
- 确保安装后环境变量已正确设置
- 验证安装：在命令行运行 `vulkaninfo`

#### 2. SDL2 (必需)
**方法1: 手动下载**
1. 访问 https://www.libsdl.org/download-2.0.php
2. 下载 "Development Libraries" for Windows (SDL2-devel-2.x.x-VC.zip)
3. 解压到 `C:\SDL2\` 目录，确保目录结构如下：
   ```
   C:\SDL2\
   ├── include\
   │   └── SDL2\
   │       ├── SDL.h
   │       └── ...
   └── lib\
       ├── x64\
       │   ├── SDL2.lib
       │   ├── SDL2main.lib
       │   └── SDL2.dll
       └── x86\
   ```

**方法2: 使用vcpkg**
```bash
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg install sdl2:x64-windows
```

**方法3: 使用包管理器**
- Chocolatey: `choco install sdl2`
- Scoop: `scoop install sdl2`

## 构建说明

### 快速开始
1. **检查依赖**: 运行 `install_dependencies.bat` 检查所需依赖
2. **构建项目**: 运行 `build.bat` 自动构建项目

### 详细构建步骤

#### 方法1: 使用提供的批处理文件（推荐）
```bash
# 首先检查依赖
install_dependencies.bat

# 然后构建项目
build.bat
```

#### 方法2: 手动构建
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

#### 方法3: 指定SDL2路径
如果SDL2安装在非标准位置：
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64 -DSDL2_DIR="C:/path/to/SDL2"
cmake --build . --config Release
```

#### 方法4: 使用MinGW
```bash
mkdir build
cd build
cmake .. -G "MinGW Makefiles"
make
```

## 运行程序

构建成功后，可执行文件位于：
- `build/bin/Release/VulkanSDLWindow.exe` (Visual Studio)
- `build/VulkanSDLWindow.exe` (MinGW)

## 控制说明

- **ESC键**: 退出程序
- **关闭按钮**: 退出程序
- **窗口可调整大小**: 支持窗口大小调整

## 项目结构

```
├── main.cpp              # 主程序入口和SDL窗口管理
├── VulkanRenderer.h      # Vulkan渲染器头文件
├── VulkanRenderer.cpp    # Vulkan渲染器实现
├── CMakeLists.txt        # CMake构建配置
├── build.bat            # Windows构建脚本
└── README.md            # 项目说明文档
```

## 故障排除

### 常见问题

1. **找不到Vulkan**: 确保已安装Vulkan SDK并设置了环境变量
2. **找不到SDL2**: 确保SDL2开发库已正确安装
3. **编译错误**: 检查CMake版本和编译器版本

### 调试信息

程序运行时会输出详细的初始化信息，如果出现问题，请查看控制台输出。

## 扩展建议

这个基础项目可以扩展为：
- 添加顶点缓冲区和图形管线
- 实现纹理加载和渲染
- 添加3D模型渲染
- 实现光照和材质系统
- 添加用户输入处理

## 许可证

此项目仅供学习和参考使用。
