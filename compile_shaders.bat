@echo off
echo Compiling shaders...

REM Check if Vulkan SDK is available
where glslc >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: glslc not found! Please make sure Vulkan SDK is installed and in PATH.
    pause
    exit /b 1
)

REM Create shaders directory if it doesn't exist
if not exist shaders mkdir shaders

REM Compile vertex shader
echo Compiling vertex shader...
glslc shaders/vertex.vert -o shaders/vert.spv
if %errorLevel% neq 0 (
    echo Error: Failed to compile vertex shader!
    pause
    exit /b 1
)

REM Compile fragment shader
echo Compiling fragment shader...
glslc shaders/fragment.frag -o shaders/frag.spv
if %errorLevel% neq 0 (
    echo Error: Failed to compile fragment shader!
    pause
    exit /b 1
)

echo Shaders compiled successfully!
echo Output files:
echo   shaders/vert.spv
echo   shaders/frag.spv
echo.
