#include "Animation.h"
#include <algorithm>
#include <iostream>

// AnimationChannel implementation
glm::mat4 AnimationChannel::getTransformationMatrix(double animationTime) const {
    glm::vec3 position = interpolatePosition(animationTime);
    glm::quat rotation = interpolateRotation(animationTime);
    glm::vec3 scaling = interpolateScaling(animationTime);

    glm::mat4 translationMatrix = glm::translate(glm::mat4(1.0f), position);
    glm::mat4 rotationMatrix = glm::mat4_cast(rotation);
    glm::mat4 scalingMatrix = glm::scale(glm::mat4(1.0f), scaling);

    return translationMatrix * rotationMatrix * scalingMatrix;
}

glm::vec3 AnimationChannel::interpolatePosition(double animationTime) const {
    if (positionKeys.size() == 1) {
        return positionKeys[0].position;
    }

    int index = findPositionIndex(animationTime);
    int nextIndex = index + 1;

    if (nextIndex >= positionKeys.size()) {
        return positionKeys[index].position;
    }

    double deltaTime = positionKeys[nextIndex].time - positionKeys[index].time;
    double factor = (animationTime - positionKeys[index].time) / deltaTime;
    
    glm::vec3 start = positionKeys[index].position;
    glm::vec3 end = positionKeys[nextIndex].position;
    
    return glm::mix(start, end, static_cast<float>(factor));
}

glm::quat AnimationChannel::interpolateRotation(double animationTime) const {
    if (rotationKeys.size() == 1) {
        return rotationKeys[0].rotation;
    }

    int index = findRotationIndex(animationTime);
    int nextIndex = index + 1;

    if (nextIndex >= rotationKeys.size()) {
        return rotationKeys[index].rotation;
    }

    double deltaTime = rotationKeys[nextIndex].time - rotationKeys[index].time;
    double factor = (animationTime - rotationKeys[index].time) / deltaTime;
    
    glm::quat start = rotationKeys[index].rotation;
    glm::quat end = rotationKeys[nextIndex].rotation;
    
    return glm::slerp(start, end, static_cast<float>(factor));
}

glm::vec3 AnimationChannel::interpolateScaling(double animationTime) const {
    if (scalingKeys.size() == 1) {
        return scalingKeys[0].scaling;
    }

    int index = findScalingIndex(animationTime);
    int nextIndex = index + 1;

    if (nextIndex >= scalingKeys.size()) {
        return scalingKeys[index].scaling;
    }

    double deltaTime = scalingKeys[nextIndex].time - scalingKeys[index].time;
    double factor = (animationTime - scalingKeys[index].time) / deltaTime;
    
    glm::vec3 start = scalingKeys[index].scaling;
    glm::vec3 end = scalingKeys[nextIndex].scaling;
    
    return glm::mix(start, end, static_cast<float>(factor));
}

int AnimationChannel::findPositionIndex(double animationTime) const {
    for (int i = 0; i < positionKeys.size() - 1; i++) {
        if (animationTime < positionKeys[i + 1].time) {
            return i;
        }
    }
    return positionKeys.size() - 1;
}

int AnimationChannel::findRotationIndex(double animationTime) const {
    for (int i = 0; i < rotationKeys.size() - 1; i++) {
        if (animationTime < rotationKeys[i + 1].time) {
            return i;
        }
    }
    return rotationKeys.size() - 1;
}

int AnimationChannel::findScalingIndex(double animationTime) const {
    for (int i = 0; i < scalingKeys.size() - 1; i++) {
        if (animationTime < scalingKeys[i + 1].time) {
            return i;
        }
    }
    return scalingKeys.size() - 1;
}

// AnimationClip implementation
const AnimationChannel* AnimationClip::getChannel(const std::string& nodeName) const {
    for (const auto& channel : channels) {
        if (channel.nodeName == nodeName) {
            return &channel;
        }
    }
    return nullptr;
}

// SceneNode implementation
glm::mat4 SceneNode::getGlobalTransformation() const {
    if (parent) {
        return parent->getGlobalTransformation() * transformation;
    }
    return transformation;
}

// AnimationSystem implementation
AnimationSystem::AnimationSystem() {
    boneTransformations.resize(100, glm::mat4(1.0f)); // Reserve space for 100 bones
}

AnimationSystem::~AnimationSystem() {
}

void AnimationSystem::addAnimationClip(const AnimationClip& clip) {
    animationClips.push_back(clip);
}

void AnimationSystem::playAnimation(const std::string& animationName, bool loop) {
    int index = findAnimationClip(animationName);
    if (index >= 0) {
        currentAnimationIndex = index;
        currentTime = 0.0f;
        playing = true;
        paused = false;
        looping = loop;
        std::cout << "Playing animation: " << animationName << std::endl;
    } else {
        std::cerr << "Animation not found: " << animationName << std::endl;
    }
}

void AnimationSystem::stopAnimation() {
    playing = false;
    paused = false;
    currentTime = 0.0f;
}

void AnimationSystem::pauseAnimation() {
    paused = true;
}

void AnimationSystem::resumeAnimation() {
    paused = false;
}

void AnimationSystem::update(float deltaTime) {
    if (!playing || paused || currentAnimationIndex < 0) {
        return;
    }

    const AnimationClip& currentClip = animationClips[currentAnimationIndex];
    currentTime += deltaTime;

    // Handle looping
    if (currentTime > currentClip.duration) {
        if (looping) {
            currentTime = fmod(currentTime, currentClip.duration);
        } else {
            currentTime = currentClip.duration;
            playing = false;
        }
    }

    // Update bone transformations
    updateBoneTransformations(sceneRoot, glm::mat4(1.0f), &currentClip);
}

const std::vector<glm::mat4>& AnimationSystem::getBoneTransformations() const {
    return boneTransformations;
}

float AnimationSystem::getDuration() const {
    if (currentAnimationIndex >= 0 && currentAnimationIndex < animationClips.size()) {
        return static_cast<float>(animationClips[currentAnimationIndex].duration);
    }
    return 0.0f;
}

void AnimationSystem::updateBoneTransformations(const SceneNode& node, const glm::mat4& parentTransform, 
                                               const AnimationClip* currentClip) {
    glm::mat4 nodeTransformation = node.transformation;

    // Apply animation if available
    if (currentClip) {
        const AnimationChannel* channel = currentClip->getChannel(node.name);
        if (channel) {
            double animationTime = currentTime * currentClip->ticksPerSecond;
            nodeTransformation = channel->getTransformationMatrix(animationTime);
        }
    }

    glm::mat4 globalTransformation = parentTransform * nodeTransformation;

    // Update bone transformation if this node is a bone
    auto it = boneMapping.find(node.name);
    if (it != boneMapping.end()) {
        int boneIndex = it->second;
        if (boneIndex < boneTransformations.size()) {
            boneTransformations[boneIndex] = globalTransformation;
        }
    }

    // Recursively update children
    for (const auto& child : node.children) {
        updateBoneTransformations(child, globalTransformation, currentClip);
    }
}

int AnimationSystem::findAnimationClip(const std::string& name) const {
    for (int i = 0; i < animationClips.size(); i++) {
        if (animationClips[i].name == name) {
            return i;
        }
    }
    return -1;
}
