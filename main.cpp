#include <SDL.h>
#include <SDL_vulkan.h>
#include <iostream>
#include <stdexcept>
#include <chrono>
#include "VulkanRenderer.h"
#include "FileDialog.h"

const int WINDOW_WIDTH = 800;
const int WINDOW_HEIGHT = 600;

class Application {
public:
    Application() : window(nullptr), renderer(nullptr) {
        lastTime = std::chrono::high_resolution_clock::now();
    }

    ~Application() {
        cleanup();
    }

    bool initialize() {
        // Initialize SDL
        if (SDL_Init(SDL_INIT_VIDEO) < 0) {
            std::cerr << "Failed to initialize SDL: " << SDL_GetError() << std::endl;
            return false;
        }

        // Create window with Vulkan support
        window = SDL_CreateWindow(
            "Vulkan SDL Window",
            SDL_WINDOWPOS_CENTERED,
            SDL_WINDOWPOS_CENTERED,
            WINDOW_WIDTH,
            WINDOW_HEIGHT,
            SDL_WINDOW_VULKAN | SDL_WINDOW_SHOWN | SDL_WINDOW_RESIZABLE
        );

        if (!window) {
            std::cerr << "Failed to create SDL window: " << SDL_GetError() << std::endl;
            return false;
        }

        // Initialize Vulkan renderer
        renderer = new VulkanRenderer();
        if (!renderer->initialize(window)) {
            std::cerr << "Failed to initialize Vulkan renderer!" << std::endl;
            return false;
        }

        std::cout << "Application initialized successfully!" << std::endl;
        return true;
    }

    void run() {
        bool running = true;
        SDL_Event event;

        std::cout << "Starting main loop..." << std::endl;
        std::cout << "Controls:" << std::endl;
        std::cout << "  ESC - Exit" << std::endl;
        std::cout << "  F - Load FBX model" << std::endl;
        std::cout << "  Space - Load test model" << std::endl;

        while (running) {
            // Handle events
            while (SDL_PollEvent(&event)) {
                switch (event.type) {
                    case SDL_QUIT:
                        running = false;
                        break;
                    
                    case SDL_KEYDOWN:
                        if (event.key.keysym.sym == SDLK_ESCAPE) {
                            running = false;
                        } else if (event.key.keysym.sym == SDLK_f) {
                            loadFBXModel();
                        } else if (event.key.keysym.sym == SDLK_SPACE) {
                            loadTestModel();
                        }
                        break;
                    
                    case SDL_WINDOWEVENT:
                        if (event.window.event == SDL_WINDOWEVENT_RESIZED) {
                            std::cout << "Window resized to: " 
                                      << event.window.data1 << "x" << event.window.data2 << std::endl;
                            // Note: In a full implementation, you would handle swapchain recreation here
                        }
                        break;
                }
            }

            // Calculate delta time
            auto currentTime = std::chrono::high_resolution_clock::now();
            float deltaTime = std::chrono::duration<float, std::chrono::seconds::period>(currentTime - lastTime).count();
            lastTime = currentTime;

            // Update models (animations)
            renderer->updateModels(deltaTime);

            // Render frame
            try {
                renderer->drawFrame();
            } catch (const std::exception& e) {
                std::cerr << "Rendering error: " << e.what() << std::endl;
                running = false;
            }

            // Small delay to prevent excessive CPU usage
            SDL_Delay(1);
        }

        std::cout << "Exiting main loop..." << std::endl;
    }

    void cleanup() {
        if (renderer) {
            delete renderer;
            renderer = nullptr;
        }

        if (window) {
            SDL_DestroyWindow(window);
            window = nullptr;
        }

        SDL_Quit();
        std::cout << "Application cleaned up." << std::endl;
    }

private:
    SDL_Window* window;
    VulkanRenderer* renderer;
    std::chrono::high_resolution_clock::time_point lastTime;

    void loadFBXModel() {
        std::string filename = FileDialog::openFBXFile();
        if (!filename.empty()) {
            std::cout << "Loading FBX model: " << filename << std::endl;
            if (renderer->loadModel(filename)) {
                std::cout << "Model loaded successfully!" << std::endl;
            } else {
                std::cout << "Failed to load model!" << std::endl;
            }
        }
    }

    void loadTestModel() {
        std::cout << "Loading test model..." << std::endl;
        if (renderer->loadModel("")) { // Empty filename triggers fallback cube
            std::cout << "Test model loaded successfully!" << std::endl;
        } else {
            std::cout << "Failed to load test model!" << std::endl;
        }
    }
};

int main(int argc, char* argv[]) {
    std::cout << "Starting Vulkan SDL Application..." << std::endl;

    try {
        Application app;
        
        if (!app.initialize()) {
            std::cerr << "Failed to initialize application!" << std::endl;
            return -1;
        }

        app.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Application error: " << e.what() << std::endl;
        return -1;
    }

    std::cout << "Application finished successfully." << std::endl;
    return 0;
}
