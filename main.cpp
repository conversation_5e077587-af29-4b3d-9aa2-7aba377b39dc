#include <SDL.h>
#include <SDL_vulkan.h>
#include <iostream>
#include <stdexcept>
#include "VulkanRenderer.h"

const int WINDOW_WIDTH = 800;
const int WINDOW_HEIGHT = 600;

class Application {
public:
    Application() : window(nullptr), renderer(nullptr) {}

    ~Application() {
        cleanup();
    }

    bool initialize() {
        // Initialize SDL
        if (SDL_Init(SDL_INIT_VIDEO) < 0) {
            std::cerr << "Failed to initialize SDL: " << SDL_GetError() << std::endl;
            return false;
        }

        // Create window with Vulkan support
        window = SDL_CreateWindow(
            "Vulkan SDL Window",
            SDL_WINDOWPOS_CENTERED,
            SDL_WINDOWPOS_CENTERED,
            WINDOW_WIDTH,
            WINDOW_HEIGHT,
            SDL_WINDOW_VULKAN | SDL_WINDOW_SHOWN | SDL_WINDOW_RESIZABLE
        );

        if (!window) {
            std::cerr << "Failed to create SDL window: " << SDL_GetError() << std::endl;
            return false;
        }

        // Initialize Vulkan renderer
        renderer = new VulkanRenderer();
        if (!renderer->initialize(window)) {
            std::cerr << "Failed to initialize Vulkan renderer!" << std::endl;
            return false;
        }

        std::cout << "Application initialized successfully!" << std::endl;
        return true;
    }

    void run() {
        bool running = true;
        SDL_Event event;

        std::cout << "Starting main loop..." << std::endl;
        std::cout << "Press ESC or close window to exit." << std::endl;

        while (running) {
            // Handle events
            while (SDL_PollEvent(&event)) {
                switch (event.type) {
                    case SDL_QUIT:
                        running = false;
                        break;
                    
                    case SDL_KEYDOWN:
                        if (event.key.keysym.sym == SDLK_ESCAPE) {
                            running = false;
                        }
                        break;
                    
                    case SDL_WINDOWEVENT:
                        if (event.window.event == SDL_WINDOWEVENT_RESIZED) {
                            std::cout << "Window resized to: " 
                                      << event.window.data1 << "x" << event.window.data2 << std::endl;
                            // Note: In a full implementation, you would handle swapchain recreation here
                        }
                        break;
                }
            }

            // Render frame
            try {
                renderer->drawFrame();
            } catch (const std::exception& e) {
                std::cerr << "Rendering error: " << e.what() << std::endl;
                running = false;
            }

            // Small delay to prevent excessive CPU usage
            SDL_Delay(1);
        }

        std::cout << "Exiting main loop..." << std::endl;
    }

    void cleanup() {
        if (renderer) {
            delete renderer;
            renderer = nullptr;
        }

        if (window) {
            SDL_DestroyWindow(window);
            window = nullptr;
        }

        SDL_Quit();
        std::cout << "Application cleaned up." << std::endl;
    }

private:
    SDL_Window* window;
    VulkanRenderer* renderer;
};

int main(int argc, char* argv[]) {
    std::cout << "Starting Vulkan SDL Application..." << std::endl;

    try {
        Application app;
        
        if (!app.initialize()) {
            std::cerr << "Failed to initialize application!" << std::endl;
            return -1;
        }

        app.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Application error: " << e.what() << std::endl;
        return -1;
    }

    std::cout << "Application finished successfully." << std::endl;
    return 0;
}
