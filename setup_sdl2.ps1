# PowerShell script to automatically download and setup SDL2
param(
    [string]$InstallPath = "C:\SDL2"
)

Write-Host "SDL2 Auto-Setup Script" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Warning: Not running as administrator. May not be able to create directories in C:\" -ForegroundColor Yellow
    Write-Host "You can continue, but if it fails, please run as administrator." -ForegroundColor Yellow
    Write-Host ""
}

# SDL2 download information
$sdl2Version = "2.28.5"  # Update this to the latest version
$sdl2Url = "https://github.com/libsdl-org/SDL/releases/download/release-$sdl2Version/SDL2-devel-$sdl2Version-VC.zip"
$tempDir = "$env:TEMP\SDL2Setup"
$zipFile = "$tempDir\SDL2-devel.zip"

Write-Host "Setting up SDL2 version $sdl2Version" -ForegroundColor Cyan
Write-Host "Install path: $InstallPath" -ForegroundColor Cyan
Write-Host ""

# Create temporary directory
Write-Host "Creating temporary directory..." -ForegroundColor Yellow
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Download SDL2
Write-Host "Downloading SDL2 from GitHub..." -ForegroundColor Yellow
Write-Host "URL: $sdl2Url" -ForegroundColor Gray

try {
    # Use Invoke-WebRequest to download
    $ProgressPreference = 'SilentlyContinue'  # Hide progress bar for cleaner output
    Invoke-WebRequest -Uri $sdl2Url -OutFile $zipFile -UseBasicParsing
    Write-Host "✓ Download completed successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual download instructions:" -ForegroundColor Yellow
    Write-Host "1. Visit: https://github.com/libsdl-org/SDL/releases" -ForegroundColor White
    Write-Host "2. Download: SDL2-devel-$sdl2Version-VC.zip" -ForegroundColor White
    Write-Host "3. Extract to: $InstallPath" -ForegroundColor White
    exit 1
}

# Extract SDL2
Write-Host "Extracting SDL2..." -ForegroundColor Yellow

try {
    # Create install directory
    if (Test-Path $InstallPath) {
        Write-Host "Removing existing SDL2 installation..." -ForegroundColor Yellow
        Remove-Item $InstallPath -Recurse -Force
    }
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null

    # Extract zip file
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($zipFile, $tempDir)
    
    # Find the extracted SDL2 folder (it should be something like SDL2-2.28.5)
    $extractedFolder = Get-ChildItem $tempDir -Directory | Where-Object { $_.Name -like "SDL2-*" } | Select-Object -First 1
    
    if ($extractedFolder) {
        # Copy contents to install path
        Copy-Item "$($extractedFolder.FullName)\*" $InstallPath -Recurse -Force
        Write-Host "✓ Extraction completed successfully" -ForegroundColor Green
    } else {
        throw "Could not find extracted SDL2 folder"
    }
} catch {
    Write-Host "✗ Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify installation
Write-Host "Verifying installation..." -ForegroundColor Yellow

$requiredFiles = @(
    "$InstallPath\include\SDL2\SDL.h",
    "$InstallPath\lib\x64\SDL2.lib",
    "$InstallPath\lib\x64\SDL2main.lib",
    "$InstallPath\lib\x64\SDL2.dll"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing: $file" -ForegroundColor Red
        $allFilesExist = $false
    }
}

# Cleanup
Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
Remove-Item $tempDir -Recurse -Force

# Final status
Write-Host ""
if ($allFilesExist) {
    Write-Host "🎉 SDL2 setup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can now build the Vulkan SDL project by running:" -ForegroundColor Cyan
    Write-Host "  .\build.bat" -ForegroundColor White
} else {
    Write-Host "❌ SDL2 setup failed!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check the error messages above and try again." -ForegroundColor Yellow
    Write-Host "You may need to run this script as administrator." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
