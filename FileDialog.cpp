#include "FileDialog.h"
#include <iostream>

#ifdef _WIN32
#include <windows.h>
#include <commdlg.h>
#include <shlobj.h>
#endif

FileDialog::FileDialog() {
}

FileDialog::~FileDialog() {
}

std::string FileDialog::openFile(const std::string& title, const std::vector<FileFilter>& filters) {
#ifdef _WIN32
    return openFileWindows(title, filters);
#else
    return openFileConsole(title, filters);
#endif
}

std::string FileDialog::saveFile(const std::string& title, const std::vector<FileFilter>& filters) {
#ifdef _WIN32
    return saveFileWindows(title, filters);
#else
    return saveFileConsole(title, filters);
#endif
}

std::string FileDialog::openFBXFile() {
    std::vector<FileFilter> filters = {
        {"FBX Files", "*.fbx"},
        {"All Files", "*.*"}
    };
    FileDialog dialog;
    return dialog.openFile("Select FBX Model", filters);
}

std::string FileDialog::openModelFile() {
    std::vector<FileFilter> filters = {
        {"3D Model Files", "*.fbx;*.obj;*.dae;*.3ds"},
        {"FBX Files", "*.fbx"},
        {"OBJ Files", "*.obj"},
        {"Collada Files", "*.dae"},
        {"3DS Files", "*.3ds"},
        {"All Files", "*.*"}
    };
    FileDialog dialog;
    return dialog.openFile("Select 3D Model", filters);
}

#ifdef _WIN32
std::string FileDialog::openFileWindows(const std::string& title, const std::vector<FileFilter>& filters) {
    OPENFILENAMEA ofn;
    char szFile[260] = {0};

    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile);
    ofn.lpstrFilter = buildFilterString(filters).c_str();
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = NULL;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = NULL;
    ofn.lpstrTitle = title.c_str();
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_NOCHANGEDIR;

    if (GetOpenFileNameA(&ofn) == TRUE) {
        return std::string(szFile);
    }

    DWORD error = CommDlgExtendedError();
    if (error != 0) {
        lastError = "File dialog error: " + std::to_string(error);
    }

    return "";
}

std::string FileDialog::saveFileWindows(const std::string& title, const std::vector<FileFilter>& filters) {
    OPENFILENAMEA ofn;
    char szFile[260] = {0};

    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile);
    ofn.lpstrFilter = buildFilterString(filters).c_str();
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = NULL;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = NULL;
    ofn.lpstrTitle = title.c_str();
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_NOCHANGEDIR | OFN_OVERWRITEPROMPT;

    if (GetSaveFileNameA(&ofn) == TRUE) {
        return std::string(szFile);
    }

    DWORD error = CommDlgExtendedError();
    if (error != 0) {
        lastError = "File dialog error: " + std::to_string(error);
    }

    return "";
}

std::string FileDialog::buildFilterString(const std::vector<FileFilter>& filters) {
    std::string filterStr;
    
    for (const auto& filter : filters) {
        filterStr += filter.description + '\0';
        filterStr += filter.extension + '\0';
    }
    
    if (filterStr.empty()) {
        filterStr = "All Files\0*.*\0";
    }
    
    filterStr += '\0'; // Double null terminator
    return filterStr;
}

#else
std::string FileDialog::openFileConsole(const std::string& title, const std::vector<FileFilter>& filters) {
    std::cout << title << std::endl;
    std::cout << "Available filters:" << std::endl;
    for (const auto& filter : filters) {
        std::cout << "  " << filter.description << " (" << filter.extension << ")" << std::endl;
    }
    std::cout << "Enter file path: ";
    
    std::string path;
    std::getline(std::cin, path);
    return path;
}

std::string FileDialog::saveFileConsole(const std::string& title, const std::vector<FileFilter>& filters) {
    return openFileConsole(title, filters); // Same implementation for console
}
#endif
