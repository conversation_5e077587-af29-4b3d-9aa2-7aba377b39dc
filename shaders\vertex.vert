#version 450

layout(binding = 0) uniform UniformBufferObject {
    mat4 model;
    mat4 view;
    mat4 proj;
    mat4 bones[100];

    // Lighting data
    vec3 lightDirection;
    float lightIntensity;
    vec3 lightColor;
    float padding1;
    vec3 ambientLight;
    float padding2;
    vec3 cameraPosition;
    float padding3;
} ubo;

layout(location = 0) in vec3 inPosition;
layout(location = 1) in vec3 inNormal;
layout(location = 2) in vec2 inTexCoord;
layout(location = 3) in ivec4 inBoneIds;
layout(location = 4) in vec4 inBoneWeights;

layout(location = 0) out vec3 fragColor;
layout(location = 1) out vec2 fragTexCoord;
layout(location = 2) out vec3 fragNormal;
layout(location = 3) out vec3 fragWorldPos;
layout(location = 4) out vec3 fragViewPos;

void main() {
    // Calculate bone transformation
    mat4 boneTransform = mat4(0.0);
    
    // Apply bone weights if any bone is assigned
    if (inBoneIds.x >= 0) {
        boneTransform += ubo.bones[inBoneIds.x] * inBoneWeights.x;
    }
    if (inBoneIds.y >= 0) {
        boneTransform += ubo.bones[inBoneIds.y] * inBoneWeights.y;
    }
    if (inBoneIds.z >= 0) {
        boneTransform += ubo.bones[inBoneIds.z] * inBoneWeights.z;
    }
    if (inBoneIds.w >= 0) {
        boneTransform += ubo.bones[inBoneIds.w] * inBoneWeights.w;
    }
    
    // If no bones are assigned, use identity matrix
    if (inBoneIds.x < 0) {
        boneTransform = mat4(1.0);
    }
    
    // Apply bone transformation to position and normal
    vec4 animatedPosition = boneTransform * vec4(inPosition, 1.0);
    vec3 animatedNormal = mat3(boneTransform) * inNormal;
    
    // Calculate world position
    vec4 worldPos = ubo.model * animatedPosition;
    fragWorldPos = worldPos.xyz;

    // Calculate view position
    vec4 viewPos = ubo.view * worldPos;
    fragViewPos = viewPos.xyz;

    // Calculate final position
    gl_Position = ubo.proj * viewPos;

    // Pass through other attributes
    fragColor = vec3(0.8, 0.8, 0.8); // Default gray color
    fragTexCoord = inTexCoord;
    fragNormal = normalize(mat3(ubo.model) * animatedNormal);
}
