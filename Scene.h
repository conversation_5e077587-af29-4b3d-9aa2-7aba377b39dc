#pragma once

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <vector>
#include <memory>
#include "Mesh.h"
#include "FBXLoader.h"

// Light types
enum class LightType {
    DIRECTIONAL,
    POINT,
    SPOT
};

// Light structure
struct Light {
    LightType type = LightType::DIRECTIONAL;
    glm::vec3 position = glm::vec3(0.0f, 10.0f, 0.0f);
    glm::vec3 direction = glm::vec3(0.0f, -1.0f, 0.0f);
    glm::vec3 color = glm::vec3(1.0f, 1.0f, 1.0f);
    float intensity = 1.0f;
    float range = 100.0f;  // For point/spot lights
    float innerCone = 30.0f;  // For spot lights (degrees)
    float outerCone = 45.0f;  // For spot lights (degrees)
};

// Scene object that can be rendered
struct SceneObject {
    std::unique_ptr<FBXModel> model;
    glm::mat4 transform = glm::mat4(1.0f);
    std::string name;
    bool visible = true;
    
    SceneObject(const std::string& objectName) : name(objectName) {}
    
    void setPosition(const glm::vec3& position) {
        transform[3] = glm::vec4(position, 1.0f);
    }
    
    void setRotation(const glm::vec3& rotation) {
        glm::mat4 rotX = glm::rotate(glm::mat4(1.0f), glm::radians(rotation.x), glm::vec3(1, 0, 0));
        glm::mat4 rotY = glm::rotate(glm::mat4(1.0f), glm::radians(rotation.y), glm::vec3(0, 1, 0));
        glm::mat4 rotZ = glm::rotate(glm::mat4(1.0f), glm::radians(rotation.z), glm::vec3(0, 0, 1));
        
        glm::vec3 position = glm::vec3(transform[3]);
        transform = glm::translate(glm::mat4(1.0f), position) * rotY * rotX * rotZ;
    }
    
    void setScale(const glm::vec3& scale) {
        glm::vec3 position = glm::vec3(transform[3]);
        glm::mat4 rotation = transform;
        rotation[3] = glm::vec4(0, 0, 0, 1);
        
        transform = glm::translate(glm::mat4(1.0f), position) * rotation * glm::scale(glm::mat4(1.0f), scale);
    }
    
    glm::vec3 getPosition() const {
        return glm::vec3(transform[3]);
    }
};

// Scene class to manage all objects and lighting
class Scene {
public:
    Scene();
    ~Scene();
    
    // Scene setup
    void createDefaultScene();
    void clear();
    
    // Object management
    SceneObject* addObject(const std::string& name);
    SceneObject* getObject(const std::string& name);
    void removeObject(const std::string& name);
    const std::vector<std::unique_ptr<SceneObject>>& getObjects() const { return objects; }
    
    // Light management
    void addLight(const Light& light);
    void removeLight(size_t index);
    Light* getLight(size_t index);
    const std::vector<Light>& getLights() const { return lights; }
    void setAmbientLight(const glm::vec3& color) { ambientLight = color; }
    const glm::vec3& getAmbientLight() const { return ambientLight; }
    
    // Scene properties
    void setBackgroundColor(const glm::vec3& color) { backgroundColor = color; }
    const glm::vec3& getBackgroundColor() const { return backgroundColor; }
    
    // Update scene (animations, etc.)
    void update(float deltaTime);
    
    // Initialize Vulkan resources for all objects
    bool initializeVulkanResources(VkDevice device, VkPhysicalDevice physicalDevice, 
                                  VkCommandPool commandPool, VkQueue graphicsQueue);
    
    // Cleanup Vulkan resources
    void cleanup(VkDevice device);

private:
    std::vector<std::unique_ptr<SceneObject>> objects;
    std::vector<Light> lights;
    glm::vec3 ambientLight = glm::vec3(0.2f, 0.2f, 0.2f);
    glm::vec3 backgroundColor = glm::vec3(0.1f, 0.1f, 0.15f);
    
    // Helper methods
    void createGroundPlane();
    void setupDefaultLighting();
    std::unique_ptr<FBXModel> createPlaneModel(float width, float height, int subdivisions = 1);
};
